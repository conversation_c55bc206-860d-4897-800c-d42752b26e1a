import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Utility class for responsive design calculations
class ResponsiveUtils {
  /// Breakpoints for different screen sizes
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  /// Check if the current screen is mobile
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  /// Check if the current screen is tablet
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < desktopBreakpoint;
  }

  /// Check if the current screen is desktop
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktopBreakpoint;
  }

  /// Check if the current screen is web (tablet or desktop)
  static bool isWeb(BuildContext context) {
    return MediaQuery.of(context).size.width >= mobileBreakpoint;
  }

  /// Get maximum width for forms on web
  static double getFormMaxWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final minWidth = screenWidth * 0.2; // At least 20% of screen width

    if (isMobile(context)) {
      return double.infinity;
    } else if (isTablet(context)) {
      return math.max(500, minWidth);
    } else {
      return math.max(450, minWidth);
    }
  }

  /// Get responsive spacing
  static double getResponsiveSpacing(BuildContext context, double baseSpacing) {
    if (isMobile(context)) {
      return baseSpacing;
    } else if (isTablet(context)) {
      return baseSpacing * 1.2;
    } else {
      return baseSpacing * 1.4;
    }
  }
}

/// A responsive container for forms that constrains width on larger screens
class ResponsiveFormContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final double? maxWidth;

  const ResponsiveFormContainer({
    super.key,
    required this.child,
    this.padding,
    this.maxWidth,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isWeb = screenWidth >= ResponsiveUtils.mobileBreakpoint;

    // Calculate responsive padding
    final responsivePadding = padding ?? EdgeInsets.symmetric(
      horizontal: ResponsiveUtils.isMobile(context) ? 24.0 : 48.0,
    );

    // Calculate form width constraints
    final formMaxWidth = maxWidth ?? ResponsiveUtils.getFormMaxWidth(context);
    final minFormWidth = screenWidth * 0.2; // At least 20% of screen width

    if (isWeb) {
      return Center(
        child: Container(
          constraints: BoxConstraints(
            minWidth: minFormWidth,
            maxWidth: formMaxWidth,
          ),
          child: Padding(
            padding: responsivePadding,
            child: child,
          ),
        ),
      );
    } else {
      return Padding(
        padding: responsivePadding,
        child: child,
      );
    }
  }
}

/// A responsive form field that adapts to screen size
class ResponsiveFormField extends StatelessWidget {
  final TextEditingController controller;
  final String labelText;
  final String hintText;
  final IconData prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;

  const ResponsiveFormField({
    super.key,
    required this.controller,
    required this.labelText,
    required this.hintText,
    required this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.validator,
    this.keyboardType,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      obscureText: obscureText,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        prefixIcon: Icon(prefixIcon),
        suffixIcon: suffixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).primaryColor,
            width: 2,
          ),
        ),
        filled: true,
        fillColor: Colors.white,
        contentPadding: const EdgeInsets.all(16),
      ),
      validator: validator,
    );
  }
}
